.red{
	color: $red;
}

.text-desc{
	font-size:$font-size-small;
	color: $font-color-gray;
}

.float-r {
	float: right;
}

.float-l {
	float: left;
}

.clearfix {
  &:before,
  &:after {
    content: " "; // 1
    display: table; // 2
  }
  &:after {
    clear: both;
  }
}

.one_border {
	@include one-border;
}

.one_border:last-child::after {
	border-bottom-width: 0px;
}

.van-hairline--top-bottom::after {
  border-width: 1px 0;
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 0px;
  border-left-width: 0px;
}

.text-center{
	text-align: center;
}

.over-hide {
  overflow: hidden !important;
}

.no-pad-bottom {
  padding-bottom: 0 !important;
}

.height-fix42 {
  padding-bottom: 42px;
}